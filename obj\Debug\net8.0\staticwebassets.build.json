{"Version": 1, "Hash": "LAapIGpxLb8CyDKaQ3F629X97E3+6DhtoTJJAXtOEXA=", "Source": "HelloWorldApi", "BasePath": "_content/HelloWorldApi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "HelloWorldApi\\wwwroot", "Source": "HelloWorldApi", "ContentRoot": "C:\\net\\workspace\\helloword\\wwwroot\\", "BasePath": "_content/HelloWorldApi", "Pattern": "**"}], "Assets": [{"Identity": "C:\\net\\workspace\\helloword\\wwwroot\\about.html", "SourceId": "HelloWorldApi", "SourceType": "Discovered", "ContentRoot": "C:\\net\\workspace\\helloword\\wwwroot\\", "BasePath": "_content/HelloWorldApi", "RelativePath": "about.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\about.html"}, {"Identity": "C:\\net\\workspace\\helloword\\wwwroot\\api-demo.html", "SourceId": "HelloWorldApi", "SourceType": "Discovered", "ContentRoot": "C:\\net\\workspace\\helloword\\wwwroot\\", "BasePath": "_content/HelloWorldApi", "RelativePath": "api-demo.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\api-demo.html"}, {"Identity": "C:\\net\\workspace\\helloword\\wwwroot\\css\\style.css", "SourceId": "HelloWorldApi", "SourceType": "Discovered", "ContentRoot": "C:\\net\\workspace\\helloword\\wwwroot\\", "BasePath": "_content/HelloWorldApi", "RelativePath": "css/style.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\style.css"}, {"Identity": "C:\\net\\workspace\\helloword\\wwwroot\\index.html", "SourceId": "HelloWorldApi", "SourceType": "Discovered", "ContentRoot": "C:\\net\\workspace\\helloword\\wwwroot\\", "BasePath": "_content/HelloWorldApi", "RelativePath": "index.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}]}