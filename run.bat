@echo off
echo 正在启动.NET Hello World Web API项目...
echo.

REM 设置dotnet路径
set DOTNET_PATH=C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe

REM 检查dotnet是否存在
if not exist "%DOTNET_PATH%" (
    echo 错误: 找不到dotnet.exe，请检查路径: %DOTNET_PATH%
    echo 请确保已安装.NET 8 SDK
    pause
    exit /b 1
)

echo 使用dotnet路径: %DOTNET_PATH%
echo.

echo 1. 恢复依赖包...
"%DOTNET_PATH%" restore
if errorlevel 1 (
    echo 错误: 依赖包恢复失败
    pause
    exit /b 1
)

echo.
echo 2. 构建项目...
"%DOTNET_PATH%" build
if errorlevel 1 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

echo.
echo 3. 启动项目...
echo 项目将在以下地址启动:
echo   - HTTP:  http://localhost:5000
echo   - HTTPS: https://localhost:7071
echo   - Swagger UI: https://localhost:7071/swagger
echo.
echo 按 Ctrl+C 停止服务器
echo.

"%DOTNET_PATH%" run
