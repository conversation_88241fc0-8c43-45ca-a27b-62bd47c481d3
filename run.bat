@echo off
chcp 65001 >nul
echo Starting .NET Hello World Web API project...
echo.

REM Set dotnet path
set DOTNET_PATH=C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe

REM Check if dotnet exists
if not exist "%DOTNET_PATH%" (
    echo Error: Cannot find dotnet.exe, please check path: %DOTNET_PATH%
    echo Please make sure .NET 8 SDK is installed
    pause
    exit /b 1
)

echo Using dotnet path: %DOTNET_PATH%
echo.

echo 1. Restoring packages...
"%DOTNET_PATH%" restore
if errorlevel 1 (
    echo Error: Package restore failed
    pause
    exit /b 1
)

echo.
echo 2. Building project...
"%DOTNET_PATH%" build
if errorlevel 1 (
    echo Error: Project build failed
    pause
    exit /b 1
)

echo.
echo 3. Starting project...
echo Project will start at the following addresses:
echo   - HTTP:  http://localhost:5000
echo   - HTTPS: https://localhost:7071
echo   - Swagger UI: https://localhost:7071/swagger
echo.
echo Press Ctrl+C to stop the server
echo.

"%DOTNET_PATH%" run
