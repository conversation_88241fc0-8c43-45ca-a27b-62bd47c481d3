<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 演示 - Hello World API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .api-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .api-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .method-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
        }

        .method-get {
            background: #28a745;
        }

        .api-url {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }

        .api-description {
            color: #666;
            margin-bottom: 1.5rem;
        }

        .test-area {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .input-row {
            display: flex;
            gap: 10px;
            margin-bottom: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .input-row label {
            font-weight: 600;
            min-width: 80px;
        }

        .input-row input {
            padding: 8px 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            flex: 1;
            min-width: 200px;
        }

        .input-row input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .response-section {
            margin-top: 1rem;
        }

        .response-header {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .response-content {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .status-success {
            color: #28a745;
        }

        .status-error {
            color: #dc3545;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 2rem;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .input-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .input-row label {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 API 演示中心</h1>
        <p>测试和体验所有可用的 API 端点</p>
    </div>

    <div class="container">
        <a href="/" class="back-link">← 返回首页</a>

        <!-- Hello World API -->
        <div class="api-section">
            <h2 class="api-title">
                <span class="method-badge method-get">GET</span>
                基础问候 API
            </h2>
            <div class="api-url">GET /</div>
            <p class="api-description">返回简单的 "Hello World!" 问候消息</p>
            
            <div class="test-area">
                <button class="btn btn-primary" onclick="testHelloWorld()">测试 API</button>
                <div class="response-section" id="hello-response" style="display: none;">
                    <div class="response-header">响应:</div>
                    <div class="response-content" id="hello-content"></div>
                </div>
            </div>
        </div>

        <!-- Hello Name API -->
        <div class="api-section">
            <h2 class="api-title">
                <span class="method-badge method-get">GET</span>
                个性化问候 API
            </h2>
            <div class="api-url">GET /hello/{name}</div>
            <p class="api-description">根据提供的名字返回个性化问候消息</p>
            
            <div class="test-area">
                <div class="input-row">
                    <label>姓名:</label>
                    <input type="text" id="name-input" placeholder="请输入您的名字" value="张三">
                    <button class="btn btn-primary" onclick="testHelloName()">测试 API</button>
                </div>
                <div class="response-section" id="name-response" style="display: none;">
                    <div class="response-header">响应:</div>
                    <div class="response-content" id="name-content"></div>
                </div>
            </div>
        </div>

        <!-- Weather Forecast API -->
        <div class="api-section">
            <h2 class="api-title">
                <span class="method-badge method-get">GET</span>
                天气预报 API
            </h2>
            <div class="api-url">GET /weatherforecast</div>
            <p class="api-description">返回未来5天的模拟天气预报数据（JSON格式）</p>
            
            <div class="test-area">
                <button class="btn btn-primary" onclick="testWeatherForecast()">测试 API</button>
                <div class="response-section" id="weather-response" style="display: none;">
                    <div class="response-header">响应:</div>
                    <div class="response-content" id="weather-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function makeRequest(url, responseElementId, contentElementId, isJson = false) {
            const responseElement = document.getElementById(responseElementId);
            const contentElement = document.getElementById(contentElementId);
            
            try {
                contentElement.textContent = '正在请求中...';
                responseElement.style.display = 'block';
                
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                let data;
                if (isJson) {
                    data = await response.json();
                    contentElement.textContent = JSON.stringify(data, null, 2);
                } else {
                    data = await response.text();
                    contentElement.textContent = data;
                }
                
                contentElement.className = 'response-content status-success';
                
            } catch (error) {
                contentElement.textContent = `错误: ${error.message}`;
                contentElement.className = 'response-content status-error';
                responseElement.style.display = 'block';
            }
        }

        function testHelloWorld() {
            makeRequest('/', 'hello-response', 'hello-content');
        }

        function testHelloName() {
            const name = document.getElementById('name-input').value.trim();
            if (!name) {
                alert('请输入姓名');
                return;
            }
            makeRequest(`/hello/${encodeURIComponent(name)}`, 'name-response', 'name-content');
        }

        function testWeatherForecast() {
            makeRequest('/weatherforecast', 'weather-response', 'weather-content', true);
        }
    </script>
</body>
</html>
