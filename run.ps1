# .NET Hello World Web API 启动脚本

Write-Host "正在启动.NET Hello World Web API项目..." -ForegroundColor Green
Write-Host ""

# 设置dotnet路径
$dotnetPath = "C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe"

# 检查dotnet是否存在
if (-not (Test-Path $dotnetPath)) {
    Write-Host "错误: 找不到dotnet.exe，请检查路径: $dotnetPath" -ForegroundColor Red
    Write-Host "请确保已安装.NET 8 SDK" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "使用dotnet路径: $dotnetPath" -ForegroundColor Yellow
Write-Host ""

try {
    Write-Host "1. 恢复依赖包..." -ForegroundColor Cyan
    & $dotnetPath restore
    if ($LASTEXITCODE -ne 0) {
        throw "依赖包恢复失败"
    }

    Write-Host ""
    Write-Host "2. 构建项目..." -ForegroundColor Cyan
    & $dotnetPath build
    if ($LASTEXITCODE -ne 0) {
        throw "项目构建失败"
    }

    Write-Host ""
    Write-Host "3. 启动项目..." -ForegroundColor Cyan
    Write-Host "项目将在以下地址启动:" -ForegroundColor Green
    Write-Host "  - HTTP:  http://localhost:5000" -ForegroundColor White
    Write-Host "  - HTTPS: https://localhost:7071" -ForegroundColor White
    Write-Host "  - Swagger UI: https://localhost:7071/swagger" -ForegroundColor White
    Write-Host ""
    Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
    Write-Host ""

    & $dotnetPath run
}
catch {
    Write-Host "错误: $_" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}
