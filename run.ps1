# .NET Hello World Web API Startup Script

Write-Host "Starting .NET Hello World Web API project..." -ForegroundColor Green
Write-Host ""

# Set dotnet path
$dotnetPath = "C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe"

# Check if dotnet exists
if (-not (Test-Path $dotnetPath)) {
    Write-Host "Error: Cannot find dotnet.exe, please check path: $dotnetPath" -ForegroundColor Red
    Write-Host "Please make sure .NET 8 SDK is installed" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host "Using dotnet path: $dotnetPath" -ForegroundColor Yellow
Write-Host ""

try {
    Write-Host "1. Restoring packages..." -ForegroundColor Cyan
    & $dotnetPath restore
    if ($LASTEXITCODE -ne 0) {
        throw "Package restore failed"
    }

    Write-Host ""
    Write-Host "2. Building project..." -ForegroundColor Cyan
    & $dotnetPath build
    if ($LASTEXITCODE -ne 0) {
        throw "Project build failed"
    }

    Write-Host ""
    Write-Host "3. Starting project..." -ForegroundColor Cyan
    Write-Host "Project will start at the following addresses:" -ForegroundColor Green
    Write-Host "  - HTTP:  http://localhost:5000" -ForegroundColor White
    Write-Host "  - HTTPS: https://localhost:7071" -ForegroundColor White
    Write-Host "  - Swagger UI: https://localhost:7071/swagger" -ForegroundColor White
    Write-Host ""
    Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
    Write-Host ""

    & $dotnetPath run
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}
