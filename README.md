# .NET Hello World Web API 项目

这是一个简单的.NET 8 Web API项目，包含Hello World功能和天气预报示例API。

## 项目结构

```
HelloWorldApi/
├── HelloWorldApi.csproj    # 项目文件
├── Program.cs              # 主程序入口
├── appsettings.json        # 应用配置
├── appsettings.Development.json  # 开发环境配置
├── Properties/
│   └── launchSettings.json # 启动配置
└── README.md              # 项目说明
```

## 功能特性

- **Hello World API**: 基础的问候接口
- **天气预报API**: 示例数据接口
- **Swagger UI**: API文档和测试界面
- **开发环境配置**: 支持热重载和调试

## 如何启动项目

### 方法1: 使用提供的启动脚本 (最简单)

**推荐使用 (避免编码问题):**
```cmd
# 双击运行或在命令行中执行
start-simple.bat
```

**完整功能批处理文件:**
```cmd
# 双击运行或在命令行中执行
run.bat
```

**PowerShell脚本:**
```powershell
# 在PowerShell中执行 (不要在cmd中运行)
.\run.ps1
```

**测试.NET安装:**
```cmd
# 测试dotnet是否正常工作
test-dotnet.bat
```

**简单启动:**
```cmd
# 直接启动（需要先手动恢复依赖）
start.cmd
```

### 方法2: 使用dotnet CLI (推荐)

1. 打开命令行/终端，导航到项目目录
2. 运行以下命令：

```bash
# 使用完整路径（如果环境变量有问题）
C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe restore
C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe build
C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe run

# 或者如果dotnet在PATH中
dotnet restore
dotnet build
dotnet run
```

### 方法3: 使用Visual Studio

1. 双击 `HelloWorldApi.csproj` 文件打开Visual Studio
2. 按 F5 或点击"开始调试"按钮

### 方法4: 使用Visual Studio Code

1. 在项目目录下打开VS Code
2. 安装C# Dev Kit扩展
3. 按 F5 启动调试

## 访问应用

启动成功后，你可以访问以下地址：

- **Swagger UI**: https://localhost:7071/swagger 或 http://localhost:5000/swagger
- **Hello World**: https://localhost:7071/ 或 http://localhost:5000/
- **个性化问候**: https://localhost:7071/hello/你的名字 或 http://localhost:5000/hello/你的名字
- **天气预报**: https://localhost:7071/weatherforecast 或 http://localhost:5000/weatherforecast

## API端点说明

### 1. Hello World
- **URL**: `/`
- **方法**: GET
- **描述**: 返回简单的"Hello World!"问候
- **响应**: 纯文本

### 2. 个性化问候
- **URL**: `/hello/{name}`
- **方法**: GET
- **参数**: name (路径参数)
- **描述**: 返回个性化问候消息
- **示例**: `/hello/张三` 返回 "Hello 张三!"

### 3. 天气预报
- **URL**: `/weatherforecast`
- **方法**: GET
- **描述**: 返回5天的模拟天气预报数据
- **响应**: JSON数组

## 开发说明

### 端口配置
- HTTP: 5000
- HTTPS: 7071

### 环境变量
- `ASPNETCORE_ENVIRONMENT`: 设置为 "Development" 启用开发模式

### 日志配置
项目使用ASP.NET Core内置日志系统，配置在appsettings.json中。

## 扩展建议

1. **添加数据库**: 集成Entity Framework Core
2. **身份验证**: 添加JWT或Identity认证
3. **缓存**: 集成Redis或内存缓存
4. **测试**: 添加单元测试和集成测试
5. **Docker**: 添加Dockerfile进行容器化部署

## 故障排除

### 常见问题

1. **字符编码错误**:
   - 不要在cmd中运行PowerShell脚本(.ps1文件)
   - 使用 `start-simple.bat` 避免编码问题
   - 或者在PowerShell窗口中运行 `.\run.ps1`

2. **端口被占用**: 修改launchSettings.json中的端口号

3. **HTTPS证书问题**: 运行 `dotnet dev-certs https --trust`

4. **依赖包问题**: 运行 `dotnet restore` 重新恢复包

5. **找不到dotnet.exe**:
   - 检查路径 `C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe` 是否存在
   - 运行 `test-dotnet.bat` 测试安装

### 系统要求

- .NET 8.0 SDK 或更高版本
- Windows 10/11, macOS, 或 Linux
- 推荐使用Visual Studio 2022或VS Code

## 许可证

此项目仅用于学习和演示目的。
