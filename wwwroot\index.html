<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello World API - 欢迎页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 90%;
            text-align: center;
        }

        .logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .feature-desc {
            color: #666;
            line-height: 1.5;
        }

        .api-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #667eea;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .response-area {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
            display: none;
        }

        .response-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .response-content {
            background: #fff;
            border-radius: 5px;
            padding: 15px;
            border-left: 4px solid #667eea;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }

        .input-group {
            margin: 20px 0;
        }

        .input-group input {
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
            width: 300px;
            max-width: 100%;
            margin-right: 10px;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .api-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🌟</div>
        <h1 class="title">Hello World API</h1>
        <p class="subtitle">欢迎使用我的第一个 .NET Web API 项目</p>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">👋</div>
                <h3 class="feature-title">简单问候</h3>
                <p class="feature-desc">获取基础的 Hello World 问候消息</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <h3 class="feature-title">个性化问候</h3>
                <p class="feature-desc">输入您的名字，获取个性化的问候</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🌤️</div>
                <h3 class="feature-title">天气预报</h3>
                <p class="feature-desc">获取模拟的5天天气预报数据</p>
            </div>
        </div>

        <div class="api-buttons">
            <button class="btn btn-primary" onclick="callHelloWorld()">Hello World</button>
            <button class="btn btn-secondary" onclick="showNameInput()">个性化问候</button>
            <button class="btn btn-primary" onclick="callWeatherForecast()">天气预报</button>
            <a href="/api-demo.html" class="btn btn-secondary">API 演示中心</a>
            <a href="/about.html" class="btn btn-secondary">关于项目</a>
            <a href="/swagger" class="btn btn-secondary" target="_blank">API 文档</a>
        </div>

        <div class="input-group" id="nameInput" style="display: none;">
            <input type="text" id="userName" placeholder="请输入您的名字" />
            <button class="btn btn-primary" onclick="callHelloName()">问候</button>
        </div>

        <div class="response-area" id="responseArea">
            <div class="response-title">API 响应:</div>
            <div class="response-content" id="responseContent"></div>
        </div>
    </div>

    <script>
        async function callAPI(url, title) {
            try {
                showResponse('正在请求中...', title);
                const response = await fetch(url);
                const data = await response.text();
                showResponse(data, title);
            } catch (error) {
                showResponse('请求失败: ' + error.message, title);
            }
        }

        async function callJSONAPI(url, title) {
            try {
                showResponse('正在请求中...', title);
                const response = await fetch(url);
                const data = await response.json();
                showResponse(JSON.stringify(data, null, 2), title);
            } catch (error) {
                showResponse('请求失败: ' + error.message, title);
            }
        }

        function showResponse(content, title) {
            document.getElementById('responseContent').textContent = content;
            document.querySelector('.response-title').textContent = title + ' 响应:';
            document.getElementById('responseArea').style.display = 'block';
        }

        function callHelloWorld() {
            callAPI('/', 'Hello World API');
        }

        function showNameInput() {
            const nameInput = document.getElementById('nameInput');
            nameInput.style.display = nameInput.style.display === 'none' ? 'block' : 'none';
        }

        function callHelloName() {
            const name = document.getElementById('userName').value.trim();
            if (!name) {
                alert('请输入您的名字');
                return;
            }
            callAPI(`/hello/${encodeURIComponent(name)}`, '个性化问候 API');
        }

        function callWeatherForecast() {
            callJSONAPI('/weatherforecast', '天气预报 API');
        }
    </script>
</body>
</html>
