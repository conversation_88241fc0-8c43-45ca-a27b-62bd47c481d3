{"format": 1, "restore": {"C:\\net\\workspace\\helloword\\HelloWorldApi.csproj": {}}, "projects": {"C:\\net\\workspace\\helloword\\HelloWorldApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\net\\workspace\\helloword\\HelloWorldApi.csproj", "projectName": "HelloWorldApi", "projectPath": "C:\\net\\workspace\\helloword\\HelloWorldApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\net\\workspace\\helloword\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\net\\dotnet-sdk-8.0.412-win-x64\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}