@echo off
echo Starting .NET Hello World Web API...
echo.

set DOTNET_EXE=C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe

if not exist "%DOTNET_EXE%" (
    echo ERROR: dotnet.exe not found at %DOTNET_EXE%
    echo Please check if .NET 8 SDK is installed correctly
    pause
    exit /b 1
)

echo Restoring packages...
"%DOTNET_EXE%" restore
if errorlevel 1 goto error

echo Building project...
"%DOTNET_EXE%" build
if errorlevel 1 goto error

echo.
echo Starting web server...
echo Open your browser and go to:
echo   http://localhost:5000
echo   https://localhost:7071/swagger
echo.
echo Press Ctrl+C to stop
echo.

"%DOTNET_EXE%" run
goto end

:error
echo.
echo Build or restore failed!
pause
exit /b 1

:end
echo.
echo Server stopped.
pause
