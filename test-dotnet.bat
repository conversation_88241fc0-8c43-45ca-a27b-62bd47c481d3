@echo off
echo Testing .NET installation...
echo.

set DOTNET_EXE=C:\net\dotnet-sdk-8.0.412-win-x64\dotnet.exe

if not exist "%DOTNET_EXE%" (
    echo ERROR: dotnet.exe not found at %DOTNET_EXE%
    echo Please check the path or install .NET 8 SDK
    pause
    exit /b 1
)

echo Found dotnet.exe at: %DOTNET_EXE%
echo.

echo Checking .NET version...
"%DOTNET_EXE%" --version
echo.

echo Checking .NET info...
"%DOTNET_EXE%" --info
echo.

echo .NET test completed!
pause
