<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于项目 - Hello World API</title>
    <link rel="stylesheet" href="/css/style.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: fadeIn 1s ease-out;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            animation: fadeIn 1s ease-out 0.3s both;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }

        .content-section {
            background: white;
            border-radius: 15px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-icon {
            font-size: 2.5rem;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 2rem 0;
        }

        .tech-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .tech-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }

        .tech-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .tech-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .tech-desc {
            color: #666;
            font-size: 0.9rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-icon {
            font-size: 1.5rem;
            color: #667eea;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 2rem 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 2rem;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 20px;
            border: 2px solid #667eea;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #667eea;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .timeline-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .timeline-desc {
            color: #666;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .content-section {
                padding: 1.5rem;
            }
            
            .tech-grid,
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📚 关于项目</h1>
            <p>了解 Hello World API 项目的技术栈和特性</p>
        </div>
    </div>

    <div class="container">
        <a href="/" class="back-link">← 返回首页</a>

        <!-- 项目介绍 -->
        <div class="content-section">
            <h2 class="section-title">
                <span class="section-icon">🚀</span>
                项目简介
            </h2>
            <p style="font-size: 1.1rem; line-height: 1.8; color: #555;">
                Hello World API 是一个基于 .NET 8 构建的现代化 Web API 项目，旨在展示 .NET 生态系统的强大功能和最佳实践。
                该项目不仅提供了基础的 API 功能，还包含了美观的前端界面和完整的开发工具链。
            </p>
        </div>

        <!-- 技术栈 -->
        <div class="content-section">
            <h2 class="section-title">
                <span class="section-icon">⚡</span>
                技术栈
            </h2>
            <div class="tech-grid">
                <div class="tech-card">
                    <div class="tech-icon">🔷</div>
                    <div class="tech-name">.NET 8</div>
                    <div class="tech-desc">最新的 .NET 框架，提供高性能和现代化开发体验</div>
                </div>
                <div class="tech-card">
                    <div class="tech-icon">🌐</div>
                    <div class="tech-name">ASP.NET Core</div>
                    <div class="tech-desc">跨平台的 Web 框架，支持高并发和微服务架构</div>
                </div>
                <div class="tech-card">
                    <div class="tech-icon">📋</div>
                    <div class="tech-name">Swagger/OpenAPI</div>
                    <div class="tech-desc">自动生成 API 文档和测试界面</div>
                </div>
                <div class="tech-card">
                    <div class="tech-icon">🎨</div>
                    <div class="tech-name">HTML5 + CSS3</div>
                    <div class="tech-desc">现代化的前端技术，响应式设计</div>
                </div>
                <div class="tech-card">
                    <div class="tech-icon">⚡</div>
                    <div class="tech-name">JavaScript ES6+</div>
                    <div class="tech-desc">现代 JavaScript 特性，异步编程</div>
                </div>
                <div class="tech-card">
                    <div class="tech-icon">🔧</div>
                    <div class="tech-name">PowerShell</div>
                    <div class="tech-desc">自动化脚本和部署工具</div>
                </div>
            </div>
        </div>

        <!-- 项目特性 -->
        <div class="content-section">
            <h2 class="section-title">
                <span class="section-icon">✨</span>
                项目特性
            </h2>
            <ul class="feature-list">
                <li>
                    <span class="feature-icon">🎯</span>
                    <div>
                        <strong>RESTful API 设计</strong> - 遵循 REST 架构原则，提供清晰的 API 接口
                    </div>
                </li>
                <li>
                    <span class="feature-icon">📱</span>
                    <div>
                        <strong>响应式前端界面</strong> - 适配各种设备屏幕，提供优秀的用户体验
                    </div>
                </li>
                <li>
                    <span class="feature-icon">🔍</span>
                    <div>
                        <strong>Swagger 集成</strong> - 自动生成 API 文档，支持在线测试
                    </div>
                </li>
                <li>
                    <span class="feature-icon">🚀</span>
                    <div>
                        <strong>高性能架构</strong> - 基于 .NET 8 的高性能运行时
                    </div>
                </li>
                <li>
                    <span class="feature-icon">🔒</span>
                    <div>
                        <strong>HTTPS 支持</strong> - 内置 SSL/TLS 支持，确保数据传输安全
                    </div>
                </li>
                <li>
                    <span class="feature-icon">🛠️</span>
                    <div>
                        <strong>开发友好</strong> - 提供多种启动脚本和开发工具
                    </div>
                </li>
            </ul>
        </div>

        <!-- 项目统计 -->
        <div class="content-section">
            <h2 class="section-title">
                <span class="section-icon">📊</span>
                项目统计
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">API 端点</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">启动脚本</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">前端页面</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">响应式设计</div>
                </div>
            </div>
        </div>

        <!-- 开发历程 -->
        <div class="content-section">
            <h2 class="section-title">
                <span class="section-icon">📅</span>
                开发历程
            </h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-title">项目初始化</div>
                        <div class="timeline-desc">创建 .NET 8 Web API 项目，配置基础架构</div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-title">API 开发</div>
                        <div class="timeline-desc">实现 Hello World、个性化问候和天气预报 API</div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-title">前端界面</div>
                        <div class="timeline-desc">设计和开发响应式前端界面，提升用户体验</div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-title">文档完善</div>
                        <div class="timeline-desc">完善项目文档，添加使用说明和故障排除指南</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
